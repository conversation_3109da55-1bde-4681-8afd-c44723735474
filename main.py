import os
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import Optional

# Load environment variables from .env
load_dotenv()

# Import the agent logic
from agent.appointment_agent import run_agent

# Pydantic model for request body
class ChatRequest(BaseModel):
    message: Optional[str] = "Hello"
    session_id: Optional[str] = None

app = FastAPI(title="AI Appointment Booking Agent")

@app.get("/")
async def root():
    return {"message": "AI Appointment Booking Agent is running!", "endpoints": ["/chat"]}

@app.get("/chat")
async def chat_get():
    # Direct GET call without any parameters - simple response for testing
    return JSONResponse({
        "response": "Hello! I'm your AI appointment booking assistant. How can I help you today?",
        "session_state": {"status": "ready", "method": "GET"}
    })

@app.post("/chat")
async def chat(request: Request):
    # Initialize default values
    user_message = "Hello"
    session_id = None
    received_input = False

    # Try to get data from request body using FastAPI's built-in JSON parsing
    try:
        # Use request.json() which is the proper FastAPI way
        data = await request.json()
        if data and isinstance(data, dict):
            # Successfully parsed JSON data
            user_message = data.get("message", "Hello")
            session_id = data.get("session_id")
            received_input = "message" in data and data["message"] != ""
    except Exception:
        # If no JSON body or parsing fails, try to check if there's any body at all
        try:
            body = await request.body()
            if body:
                # There was a body but JSON parsing failed
                user_message = "Invalid JSON format received"
                received_input = True
        except Exception:
            # No body at all, use defaults
            pass

    # Return response showing what was received
    return JSONResponse({
        "response": f"I received your message: '{user_message}'. I'm your AI appointment booking assistant ready to help!",
        "session_state": {
            "user_message": user_message,
            "session_id": session_id,
            "method": "POST",
            "received_input": received_input
        }
    })

# Alternative endpoint using Pydantic model (more reliable) with AI agent
@app.post("/chat-v2")
async def chat_v2(chat_request: ChatRequest):
    """
    Chat endpoint with AI agent conversation using Pydantic model for automatic JSON parsing
    """
    try:
        # Call the AI agent with the user's message
        response, session_state = await run_agent(chat_request.message, chat_request.session_id)

        return JSONResponse({
            "response": response,
            "session_state": session_state,
            "method": "POST-V2-AI",
            "received_input": chat_request.message != "Hello"
        })
    except Exception as e:
        # Fallback response if agent fails
        return JSONResponse({
            "response": f"I'm sorry, I encountered an error: {str(e)}. But I received your message: '{chat_request.message}'. Please try again.",
            "session_state": {
                "user_message": chat_request.message,
                "session_id": chat_request.session_id,
                "error": str(e)
            },
            "method": "POST-V2-ERROR",
            "received_input": chat_request.message != "Hello"
        })
