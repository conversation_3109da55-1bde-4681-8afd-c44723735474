import os
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

# Import the agent logic (to be implemented)
from agent.appointment_agent import run_agent

app = FastAPI(title="AI Appointment Booking Agent")

@app.post("/chat")
async def chat(request: Request):
    data = await request.json()
    user_message = data.get("message", "")
    session_id = data.get("session_id")
    response, session_state = await run_agent(user_message, session_id)
    return JSONResponse({"response": response, "session_state": session_state})
