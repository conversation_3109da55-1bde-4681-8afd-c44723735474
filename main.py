import os
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

app = FastAPI(title="AI Appointment Booking Agent")

@app.get("/")
async def root():
    return {"message": "AI Appointment Booking Agent is running!", "endpoints": ["/chat"]}

@app.get("/chat")
async def chat_get():
    # Direct GET call without any parameters - simple response for testing
    return JSONResponse({
        "response": "Hello! I'm your AI appointment booking assistant. How can I help you today?",
        "session_state": {"status": "ready", "method": "GET"}
    })

@app.post("/chat")
async def chat(request: Request):
    # Initialize default values
    user_message = "Hello"
    session_id = None

    # Try to get data from request body
    try:
        # Check if request has content
        body = await request.body()
        if body:
            # Parse JSON if body exists
            import json
            data = json.loads(body.decode('utf-8'))
            user_message = data.get("message", "Hello")
            session_id = data.get("session_id")
        else:
            # No body provided, use default
            user_message = "Hello"
    except Exception:
        # If JSON parsing fails, use default
        user_message = "Hello"

    # Return response showing what was received
    return JSONResponse({
        "response": f"I received your message: '{user_message}'. I'm your AI appointment booking assistant ready to help!",
        "session_state": {
            "user_message": user_message,
            "session_id": session_id,
            "method": "POST",
            "received_input": user_message != "Hello"
        }
    })
