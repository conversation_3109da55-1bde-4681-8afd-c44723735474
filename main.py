import os
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

# Import the agent logic (to be implemented)
from agent.appointment_agent import run_agent

app = FastAPI(title="AI Appointment Booking Agent")

@app.post("/chat")
async def chat(request: Request):
    try:
        data = await request.json()
    except Exception:
        data = {}
    user_message = data.get("message", "")
    session_id = data.get("session_id")
    # Always respond, even if no message
    if not user_message:
        return JSONResponse({"response": "Hello! Please provide a message to start the conversation.", "session_state": {}})
    response, session_state = await run_agent(user_message, session_id)
    return JSONResponse({"response": response, "session_state": session_state})
