import os
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

app = FastAPI(title="AI Appointment Booking Agent")

@app.get("/")
async def root():
    return {"message": "AI Appointment Booking Agent is running!", "endpoints": ["/chat"]}

@app.get("/chat")
async def chat_get():
    # Direct GET call without any parameters - simple response for testing
    return J<PERSON>NResponse({
        "response": "Hello! I'm your AI appointment booking assistant. How can I help you today?",
        "session_state": {"status": "ready", "method": "GET"}
    })

@app.post("/chat")
async def chat(request: Request = None):
    # Default message if no input provided
    user_message = "Hello"
    session_id = None

    # Try to get data from request body if provided
    try:
        if request:
            data = await request.json()
            user_message = data.get("message", "Hello")
            session_id = data.get("session_id")
    except Exception:
        # If no valid JSON or no request, use default message
        user_message = "Hello"

    # Return simple response for testing
    return J<PERSON><PERSON><PERSON>ponse({
        "response": f"Hello! I received your message: '{user_message}'. I'm your AI appointment booking assistant ready to help!",
        "session_state": {"user_message": user_message, "session_id": session_id, "method": "POST"}
    })
