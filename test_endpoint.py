import requests
import json

# Test the root endpoint
print("Testing root endpoint...")
try:
    response = requests.get("http://127.0.0.1:8000/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
except Exception as e:
    print(f"Error testing root endpoint: {e}")
    print()

# Test the chat endpoint
print("Testing chat endpoint...")
try:
    test_data = {
        "message": "Hello, I want to book an appointment",
        "session_id": "test_session_123"
    }
    
    response = requests.post(
        "http://127.0.0.1:8000/chat",
        headers={"Content-Type": "application/json"},
        json=test_data
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
except Exception as e:
    print(f"Error testing chat endpoint: {e}")

# Test with empty message
print("\nTesting chat endpoint with empty message...")
try:
    test_data = {
        "message": "",
        "session_id": "test_session_123"
    }
    
    response = requests.post(
        "http://127.0.0.1:8000/chat",
        headers={"Content-Type": "application/json"},
        json=test_data
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
except Exception as e:
    print(f"Error testing chat endpoint with empty message: {e}")
