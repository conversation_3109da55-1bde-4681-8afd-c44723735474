import requests

print("=== Testing Endpoints Without Any Input ===\n")

# Test GET /chat (no input required)
print("1. Testing GET /chat (no input required)...")
try:
    response = requests.get("http://127.0.0.1:8000/chat")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
except Exception as e:
    print(f"Error: {e}\n")

# Test POST /chat with empty body (no input required)
print("2. Testing POST /chat with empty body...")
try:
    response = requests.post("http://127.0.0.1:8000/chat")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
except Exception as e:
    print(f"Error: {e}\n")

# Test POST /chat with empty JSON (no input required)
print("3. Testing POST /chat with empty JSON...")
try:
    response = requests.post(
        "http://127.0.0.1:8000/chat",
        headers={"Content-Type": "application/json"},
        json={}
    )
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
except Exception as e:
    print(f"Error: {e}\n")

print("=== All tests completed! ===")
print("The endpoints should work without requiring any input parameters.")
