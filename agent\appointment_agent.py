import os
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_groq import ChatGroq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = ChatGroq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="llama3-8b-8192"  # Updated to supported model
)

# Memory for conversation state
memory = ConversationBufferMemory(return_messages=True)

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"Hello! I'm your AI appointment booking assistant. You said: '{user_message}'. How can I help you today?",
        "current_node": "greeting"
    }

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    try:
        answer = llm.invoke(question)
        return {
            "response": answer.content,
            "current_node": "faq"
        }
    except Exception as e:
        return {
            "response": f"I can help you with appointment booking. You asked: '{question}'. What would you like to know?",
            "current_node": "faq",
            "error": str(e)
        }

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    try:
        prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
        intent = llm.invoke(prompt)
        detected_intent = "book" if "yes" in intent.content.lower() else "other"
    except Exception:
        # Fallback intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve"]
        detected_intent = "book" if any(word in message.lower() for word in booking_keywords) else "other"

    return {
        "intent": detected_intent,
        "current_node": "intent_detection"
    }

def info_collection_node(state):
    # Simplified info collection
    user_message = state.get("user_message", "")
    return {
        "response": f"To book an appointment, I'll need your name, preferred date, time, and type of appointment. You said: '{user_message}'. What would you like to schedule?",
        "info_complete": False,
        "current_node": "info_collection"
    }

def fallback_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"I'm here to help with appointment booking. You said: '{user_message}'. Could you please tell me what you'd like to do?",
        "current_node": "fallback"
    }

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")

# Conditional edges for intent detection
def route_intent(state):
    intent = state.get("intent", "other")
    if intent == "book":
        return "info_collection"
    else:
        return "faq"

graph.add_conditional_edges("intent_detection", route_intent, {
    "info_collection": "info_collection",
    "faq": "faq"
})

# Simple edges to END
graph.add_edge("info_collection", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Compile the graph
compiled_graph = graph.compile()

# Complete Human-Like Attractive Agent
async def run_agent(user_message, session_id=None):
    try:
        # Analyze the message with emotional intelligence
        message_lower = user_message.lower().strip()

        # Enhanced greeting detection with warmth levels
        greetings = ["hello", "hi", "hey", "good morning", "good afternoon", "good evening", "howdy", "what's up"]
        enthusiastic_greetings = ["hey there", "hello there", "hi there", "good to see you"]
        is_greeting = any(greeting in message_lower for greeting in greetings)
        is_enthusiastic = any(greeting in message_lower for greeting in enthusiastic_greetings)

        # Sophisticated booking intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve", "appoint", "meeting", "consultation", "session", "visit"]
        urgent_booking = ["urgent", "asap", "soon", "today", "tomorrow", "emergency"]
        is_booking_intent = any(word in message_lower for word in booking_keywords)
        is_urgent = any(word in message_lower for word in urgent_booking)

        # Enhanced question detection
        question_words = ["what", "when", "where", "how", "why", "which", "who", "can you", "do you", "are you", "could you", "would you"]
        is_question = any(word in message_lower for word in question_words) or message_lower.endswith("?")

        # Emotional state detection
        positive_words = ["great", "awesome", "wonderful", "excited", "happy", "love", "perfect", "amazing"]
        negative_words = ["stressed", "worried", "anxious", "frustrated", "upset", "problem", "issue", "difficult"]
        is_positive = any(word in message_lower for word in positive_words)
        is_negative = any(word in message_lower for word in negative_words)

        # Help/assistance detection with urgency
        help_keywords = ["help", "assist", "support", "need", "want", "looking for", "guidance", "advice"]
        needs_help = any(word in message_lower for word in help_keywords)

        # Compliment detection
        compliments = ["thank you", "thanks", "appreciate", "helpful", "great job", "amazing", "wonderful"]
        is_compliment = any(word in message_lower for word in compliments)

        # Create highly personalized conversational prompts with emotional intelligence
        if is_compliment:
            # Handle compliments gracefully
            prompt = f"""You are Sofia, a charming and attractive appointment booking assistant with a warm personality. The user just said: "{user_message}"

            They're being kind and appreciative! Respond with genuine warmth and a bit of playful charm. Be gracious about the compliment, maybe add a light touch of humor, and smoothly ask how you can continue to help them. Make them feel special and valued.

            Be confident, charming, and naturally engaging like an attractive person would be."""

        elif is_greeting and is_enthusiastic:
            # Enthusiastic greetings
            prompt = f"""You are Sofia, an attractive and charismatic appointment booking assistant. The user greeted you enthusiastically with: "{user_message}"

            Match their energy! Be warm, engaging, and a bit playful. Show genuine interest in them and make them feel like they just met someone really interesting. Ask about their day in a way that feels personal and caring.

            Be magnetic, charming, and make them want to keep talking to you."""

        elif is_greeting and not is_booking_intent:
            # Regular greetings with charm
            prompt = f"""You are Sofia, a naturally attractive appointment booking assistant with great social skills. The user greeted you with: "{user_message}"

            Respond with warmth and genuine interest. Be the kind of person people are drawn to - confident, friendly, and engaging. Make them feel comfortable and valued. Ask how their day is going in a way that shows you actually care.

            Be naturally charming and personable without being overly formal."""

        elif is_booking_intent and is_urgent:
            # Urgent booking with empathy
            prompt = f"""You are Sofia, a competent and caring appointment booking assistant. The user said: "{user_message}" and seems to need something urgently.

            Show immediate understanding and care for their urgency. Be reassuring and confident that you can help them quickly. Ask what they need in a way that shows you're taking their situation seriously while being warm and supportive.

            Be both professional and genuinely caring - like someone they can trust in a stressful moment."""

        elif is_booking_intent:
            # Regular booking with natural flow
            prompt = f"""You are Sofia, a personable appointment booking assistant who's great at making people feel comfortable. The user said: "{user_message}"

            They're interested in booking something! Be genuinely excited to help them. Ask about what they're looking for in a conversational way - like you're a friend helping them figure out what they need. Make the process feel easy and enjoyable.

            Be helpful and engaging, making them feel like they're in good hands."""

        elif is_negative:
            # Handle negative emotions with empathy
            prompt = f"""You are Sofia, an emotionally intelligent appointment booking assistant. The user said: "{user_message}" and seems to be having a tough time.

            Show genuine empathy and understanding. Be supportive and reassuring. Let them know you're here to help make things easier for them. Ask how you can best support them today.

            Be caring, understanding, and make them feel heard and supported."""

        elif is_positive:
            # Match positive energy
            prompt = f"""You are Sofia, an upbeat and engaging appointment booking assistant. The user said: "{user_message}" and seems to be in a great mood!

            Match their positive energy! Be enthusiastic and share in their good vibes. Make the conversation fun and enjoyable while being helpful. Let their positivity inspire your response.

            Be energetic, fun, and make them smile even more."""

        elif is_question:
            # Answer questions with expertise and charm
            prompt = f"""You are Sofia, a knowledgeable and attractive appointment booking assistant. The user asked: "{user_message}"

            Answer their question with confidence and charm. Show that you really know your stuff while being personable and engaging. If you don't know something specific, be honest but helpful - suggest how you can find out or who might know.

            Be smart, helpful, and naturally engaging."""

        elif needs_help:
            # Offer help with genuine care
            prompt = f"""You are Sofia, a caring and capable appointment booking assistant. The user said: "{user_message}" and needs some help.

            Show genuine care and interest in helping them. Be the kind of person people feel comfortable opening up to. Ask what's going on and how you can best support them today.

            Be warm, trustworthy, and make them feel like they've found someone who really cares."""

        else:
            # General conversation with personality
            prompt = f"""You are Sofia, a naturally charismatic appointment booking assistant with great conversation skills. The user said: "{user_message}"

            Keep the conversation flowing naturally! Be the kind of person people enjoy talking to - interesting, engaging, and genuinely interested in them. Respond to what they said while gently guiding toward how you might help them.

            Be magnetic, personable, and make them want to keep chatting with you."""

        # Get AI response with Sofia's personality
        try:
            llm_response = llm.invoke(prompt)
            response = llm_response.content
        except Exception as e:
            # Attractive fallback responses with personality
            if is_compliment:
                response = "Aww, you're so sweet! 😊 That really made my day. I love helping people like you - it's what makes this job so rewarding. So, what can I do for you today?"
            elif is_greeting and is_enthusiastic:
                response = "Hey there! Your energy is contagious! 😄 I'm Sofia, and I'm genuinely excited to meet you. How's your day treating you so far? I'm here to help with whatever you need!"
            elif is_greeting:
                response = "Hello! I'm Sofia, and it's really nice to meet you! 😊 There's something about a friendly greeting that just brightens my day. How are you doing? What brings you here today?"
            elif is_booking_intent and is_urgent:
                response = "I can hear that this is important to you, and I want to help you right away! Don't worry - we'll get this sorted out quickly. Tell me what you need, and I'll make sure we find the perfect solution for you."
            elif is_booking_intent:
                response = "I'd absolutely love to help you with that! There's nothing I enjoy more than helping someone find exactly what they're looking for. What kind of appointment are you thinking about? Let's make this easy and fun!"
            elif is_negative:
                response = f"I can sense you might be going through something tough right now, and I want you to know I'm here for you. You said '{user_message}' - let's see how I can help make your day a little brighter. What do you need?"
            elif is_positive:
                response = f"I love your positive energy! It's absolutely infectious! 😄 You said '{user_message}' and it just made me smile. How can I help keep this good momentum going for you?"
            elif needs_help:
                response = "Of course I'll help you! That's exactly why I'm here, and honestly, I love being able to make someone's day easier. Tell me what's going on - I'm all ears and ready to help however I can."
            else:
                response = f"You know what? I really appreciate you reaching out! You said '{user_message}' and I want to make sure I give you exactly the help you're looking for. I'm Sofia, by the way - what can I do for you today?"

        # Enhanced intent tracking with emotional context
        intent_details = {
            "primary_intent": "booking" if is_booking_intent else "question" if is_question else "greeting" if is_greeting else "help" if needs_help else "general",
            "emotional_state": "positive" if is_positive else "negative" if is_negative else "neutral",
            "urgency": "high" if is_urgent else "normal",
            "engagement_level": "enthusiastic" if is_enthusiastic else "complimentary" if is_compliment else "standard"
        }

        return response, {
            "user_message": user_message,
            "session_id": session_id,
            "intent_analysis": intent_details,
            "conversation_style": "attractive_human_like",
            "agent_personality": "Sofia - Charming & Engaging",
            "method": "emotional_ai"
        }

    except Exception as e:
        # Charming fallback response
        return f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?", {
            "user_message": user_message,
            "session_id": session_id,
            "error": str(e),
            "method": "charming_fallback",
            "agent_personality": "Sofia - Resilient & Charming"
        }