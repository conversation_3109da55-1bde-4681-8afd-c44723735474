import os
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_groq import ChatGroq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = ChatGroq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="llama3-8b-8192"  # Updated to supported model
)

# Memory for conversation state
memory = ConversationBufferMemory(return_messages=True)

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"Hello! I'm your AI appointment booking assistant. You said: '{user_message}'. How can I help you today?",
        "current_node": "greeting"
    }

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    try:
        answer = llm.invoke(question)
        return {
            "response": answer.content,
            "current_node": "faq"
        }
    except Exception as e:
        return {
            "response": f"I can help you with appointment booking. You asked: '{question}'. What would you like to know?",
            "current_node": "faq",
            "error": str(e)
        }

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    try:
        prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
        intent = llm.invoke(prompt)
        detected_intent = "book" if "yes" in intent.content.lower() else "other"
    except Exception:
        # Fallback intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve"]
        detected_intent = "book" if any(word in message.lower() for word in booking_keywords) else "other"

    return {
        "intent": detected_intent,
        "current_node": "intent_detection"
    }

def info_collection_node(state):
    # Simplified info collection
    user_message = state.get("user_message", "")
    return {
        "response": f"To book an appointment, I'll need your name, preferred date, time, and type of appointment. You said: '{user_message}'. What would you like to schedule?",
        "info_complete": False,
        "current_node": "info_collection"
    }

def fallback_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"I'm here to help with appointment booking. You said: '{user_message}'. Could you please tell me what you'd like to do?",
        "current_node": "fallback"
    }

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")

# Conditional edges for intent detection
def route_intent(state):
    intent = state.get("intent", "other")
    if intent == "book":
        return "info_collection"
    else:
        return "faq"

graph.add_conditional_edges("intent_detection", route_intent, {
    "info_collection": "info_collection",
    "faq": "faq"
})

# Simple edges to END
graph.add_edge("info_collection", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Compile the graph
compiled_graph = graph.compile()

# Human-like conversational agent
async def run_agent(user_message, session_id=None):
    try:
        # Analyze the message for natural conversation
        message_lower = user_message.lower().strip()

        # Greeting detection
        greetings = ["hello", "hi", "hey", "good morning", "good afternoon", "good evening"]
        is_greeting = any(greeting in message_lower for greeting in greetings)

        # Booking intent detection (more nuanced)
        booking_keywords = ["book", "appointment", "schedule", "reserve", "appoint", "meeting", "consultation"]
        is_booking_intent = any(word in message_lower for word in booking_keywords)

        # Question detection
        question_words = ["what", "when", "where", "how", "why", "which", "who", "can you", "do you", "are you"]
        is_question = any(word in message_lower for word in question_words) or message_lower.endswith("?")

        # Help/assistance detection
        help_keywords = ["help", "assist", "support", "need", "want", "looking for"]
        needs_help = any(word in message_lower for word in help_keywords)

        # Create conversational prompt based on context
        if is_greeting and not is_booking_intent:
            # Natural greeting response
            prompt = f"""You are a friendly, warm appointment booking assistant. The user just greeted you with: "{user_message}"

            Respond naturally and warmly like a human would. Be conversational, ask how you can help them today, and mention that you can help with appointments if they need it. Keep it natural and not too formal.

            Don't immediately jump into booking - just be friendly and see what they need."""

        elif is_booking_intent:
            # Booking conversation
            prompt = f"""You are a helpful appointment booking assistant. The user said: "{user_message}"

            They seem interested in booking something. Respond conversationally and naturally like a human would. Ask what kind of appointment they're looking for in a friendly way. Don't give them a rigid form - just have a natural conversation about what they need.

            Be warm, helpful, and conversational."""

        elif is_question:
            # Answer questions naturally
            prompt = f"""You are a friendly appointment booking assistant. The user asked: "{user_message}"

            Answer their question naturally and conversationally like a human would. If it's about appointments, services, or scheduling, provide helpful information. If you don't know specific details, be honest but helpful.

            Keep the conversation flowing naturally."""

        elif needs_help:
            # Help/assistance
            prompt = f"""You are a caring appointment booking assistant. The user said: "{user_message}"

            They seem to need help with something. Respond warmly and ask what you can help them with today. Be conversational and human-like. Let them know you're here to help with appointments or answer any questions they might have.

            Be supportive and friendly."""

        else:
            # General conversation
            prompt = f"""You are a friendly appointment booking assistant having a natural conversation. The user said: "{user_message}"

            Respond naturally and conversationally like a human would. Keep the conversation flowing, be helpful, and if appropriate, gently mention that you can help with appointments if they need it.

            Be warm, personable, and engaging."""

        # Get AI response
        try:
            llm_response = llm.invoke(prompt)
            response = llm_response.content
        except Exception as e:
            # Natural fallback
            if is_greeting:
                response = "Hello! It's great to hear from you. How are you doing today? I'm here to help with anything you need - whether that's booking an appointment or just answering questions. What can I do for you?"
            elif is_booking_intent:
                response = "I'd love to help you with that! What kind of appointment are you thinking about? I'm here to make the process as easy as possible for you."
            else:
                response = f"Thanks for reaching out! I heard you say '{user_message}'. I'm here to help with whatever you need. How can I assist you today?"

        # Determine intent for session tracking
        if is_booking_intent:
            intent = "booking"
        elif is_question:
            intent = "question"
        elif is_greeting:
            intent = "greeting"
        elif needs_help:
            intent = "help"
        else:
            intent = "general"

        return response, {
            "user_message": user_message,
            "session_id": session_id,
            "intent": intent,
            "conversation_style": "human-like",
            "method": "conversational_ai"
        }

    except Exception as e:
        # Natural fallback response
        return f"Hi there! I caught what you said: '{user_message}'. I'm your friendly appointment assistant, and I'm here to help with whatever you need. What can I do for you today?", {
            "user_message": user_message,
            "session_id": session_id,
            "error": str(e),
            "method": "natural_fallback"
        }