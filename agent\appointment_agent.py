import os
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_groq import ChatGroq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = ChatGroq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="mixtral-8x7b-32768"  # or "llama3-70b-8192" if preferred
)

# Memory for conversation state
memory = ConversationBufferMemory(return_messages=True)

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    return {"response": "Hello! How can I help you today?"}

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    answer = llm.invoke(question)
    return {"response": answer.content}

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
    intent = llm.invoke(prompt)
    return {"intent": "book" if "yes" in intent.content.lower() else "other"}

def info_collection_node(state):
    # Collect missing info (name, type, date, time)
    required = ["name", "appointment_type", "date", "time"]
    missing = [k for k in required if not state.get(k)]
    if missing:
        return {"response": f"Please provide your {', '.join(missing)}."}
    return {"info_complete": True}

def calendar_check_node(state):
    # Check availability
    available = check_availability(state["date"], state["time"])
    return {"available": available}

def confirmation_node(state):
    # Confirm details with user
    return {"response": f"Confirming: {state['appointment_type']} for {state['name']} on {state['date']} at {state['time']}. Is this correct? (yes/no)"}

def booking_node(state):
    # Book the appointment
    result = book_appointment(state["name"], state["appointment_type"], state["date"], state["time"])
    return {"response": f"Your appointment is booked! Details: {result}"}

def fallback_node(state):
    return {"response": "Sorry, I didn't understand that. Could you please rephrase?"}

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("calendar_check", calendar_check_node)
graph.add_node("confirmation", confirmation_node)
graph.add_node("booking", booking_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")

# Conditional edges for intent detection
def route_intent(state):
    intent = state.get("intent")
    if intent == "book":
        return "info_collection"
    else:
        return "faq"

graph.add_conditional_edges("intent_detection", route_intent, {
    "info_collection": "info_collection",
    "faq": "faq"
})

# Conditional edges for info collection
def route_info_collection(state):
    if state.get("info_complete"):
        return "calendar_check"
    else:
        return "info_collection"

graph.add_conditional_edges("info_collection", route_info_collection, {
    "calendar_check": "calendar_check",
    "info_collection": "info_collection"
})

# Conditional edges for calendar check
def route_calendar_check(state):
    if state.get("available"):
        return "confirmation"
    else:
        return "fallback"

graph.add_conditional_edges("calendar_check", route_calendar_check, {
    "confirmation": "confirmation",
    "fallback": "fallback"
})

# Conditional edges for confirmation
def route_confirmation(state):
    user_message = state.get("user_message", "").lower()
    if "yes" in user_message:
        return "booking"
    elif "no" in user_message:
        return "info_collection"
    else:
        return "confirmation"

graph.add_conditional_edges("confirmation", route_confirmation, {
    "booking": "booking",
    "info_collection": "info_collection",
    "confirmation": "confirmation"
})

graph.add_edge("booking", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Compile the graph
compiled_graph = graph.compile()

# Exported function for FastAPI
async def run_agent(user_message, session_id=None):
    # Use session_id for memory if needed
    state = AppointmentState(user_message=user_message)
    result = compiled_graph.invoke(state)
    response = result.get("response", "Sorry, something went wrong.")
    return response, dict(result)