import os
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_community.llms.groq import Groq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = Groq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="mixtral-8x7b-32768"  # or "llama3-70b-8192" if preferred
)

# Memory for conversation state
memory = ConversationBufferMemory(return_messages=True)

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    return {"response": "Hello! How can I help you today?"}

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    answer = llm(question)
    return {"response": answer}

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
    intent = llm(prompt)
    return {"intent": "book" if "yes" in intent.lower() else "other"}

def info_collection_node(state):
    # Collect missing info (name, type, date, time)
    required = ["name", "appointment_type", "date", "time"]
    missing = [k for k in required if not state.get(k)]
    if missing:
        return {"response": f"Please provide your {', '.join(missing)}."}
    return {"info_complete": True}

def calendar_check_node(state):
    # Check availability
    available = check_availability(state["date"], state["time"])
    return {"available": available}

def confirmation_node(state):
    # Confirm details with user
    return {"response": f"Confirming: {state['appointment_type']} for {state['name']} on {state['date']} at {state['time']}. Is this correct? (yes/no)"}

def booking_node(state):
    # Book the appointment
    result = book_appointment(state["name"], state["appointment_type"], state["date"], state["time"])
    return {"response": f"Your appointment is booked! Details: {result}"}

def fallback_node(state):
    return {"response": "Sorry, I didn't understand that. Could you please rephrase?"}

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("calendar_check", calendar_check_node)
graph.add_node("confirmation", confirmation_node)
graph.add_node("booking", booking_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")
graph.add_edge("intent_detection", "faq", condition=lambda s: s.get("intent") != "book")
graph.add_edge("intent_detection", "info_collection", condition=lambda s: s.get("intent") == "book")
graph.add_edge("info_collection", "calendar_check", condition=lambda s: s.get("info_complete"))
graph.add_edge("info_collection", "info_collection", condition=lambda s: not s.get("info_complete"))
graph.add_edge("calendar_check", "confirmation", condition=lambda s: s.get("available"))
graph.add_edge("calendar_check", "fallback", condition=lambda s: not s.get("available"))
graph.add_edge("confirmation", "booking", condition=lambda s: "yes" in s.get("user_message", "").lower())
graph.add_edge("confirmation", "info_collection", condition=lambda s: "no" in s.get("user_message", "").lower())
graph.add_edge("booking", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Exported function for FastAPI
async def run_agent(user_message, session_id=None):
    # Use session_id for memory if needed
    state = AppointmentState(user_message=user_message)
    result = graph.run(state)
    response = result.get("response", "Sorry, something went wrong.")
    return response, dict(result) 