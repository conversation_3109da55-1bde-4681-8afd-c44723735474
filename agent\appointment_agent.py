import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_groq import ChatGroq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = ChatGroq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="llama3-8b-8192"  # Updated to supported model
)

# Advanced Memory System
class ConversationMemory:
    def __init__(self):
        self.short_term_memory = {}  # Session-based memory
        self.long_term_memory = {}   # Persistent user memory
        self.memory_file = "user_memories.json"
        self.load_long_term_memory()

    def load_long_term_memory(self):
        """Load persistent memory from file"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r') as f:
                    self.long_term_memory = json.load(f)
        except Exception:
            self.long_term_memory = {}

    def save_long_term_memory(self):
        """Save persistent memory to file"""
        try:
            with open(self.memory_file, 'w') as f:
                json.dump(self.long_term_memory, f, indent=2)
        except Exception:
            pass

    def add_to_short_term(self, session_id: str, message: str, response: str, context: Dict):
        """Add to short-term (session) memory"""
        if session_id not in self.short_term_memory:
            self.short_term_memory[session_id] = {
                "messages": [],
                "context": {},
                "started_at": datetime.now().isoformat(),
                "last_interaction": datetime.now().isoformat()
            }

        self.short_term_memory[session_id]["messages"].append({
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "sofia_response": response,
            "context": context
        })

        # Keep only last 20 messages for short-term
        if len(self.short_term_memory[session_id]["messages"]) > 20:
            self.short_term_memory[session_id]["messages"] = self.short_term_memory[session_id]["messages"][-20:]

        self.short_term_memory[session_id]["last_interaction"] = datetime.now().isoformat()

        # Update context
        self.short_term_memory[session_id]["context"].update(context)

    def add_to_long_term(self, session_id: str, user_info: Dict):
        """Add to long-term (persistent) memory"""
        user_key = f"user_{session_id}" if session_id else "anonymous"

        if user_key not in self.long_term_memory:
            self.long_term_memory[user_key] = {
                "first_met": datetime.now().isoformat(),
                "total_conversations": 0,
                "preferences": {},
                "personal_info": {},
                "appointment_history": [],
                "relationship_notes": []
            }

        # Update user info
        self.long_term_memory[user_key]["total_conversations"] += 1
        self.long_term_memory[user_key]["last_seen"] = datetime.now().isoformat()

        # Merge new info
        for key, value in user_info.items():
            if key in ["name", "preferences", "personal_info"]:
                if key == "preferences":
                    self.long_term_memory[user_key]["preferences"].update(value)
                elif key == "personal_info":
                    self.long_term_memory[user_key]["personal_info"].update(value)
                else:
                    self.long_term_memory[user_key][key] = value

        self.save_long_term_memory()

    def get_conversation_context(self, session_id: str) -> str:
        """Get conversation context for AI prompt"""
        context_parts = []

        # Short-term memory
        if session_id and session_id in self.short_term_memory:
            recent_messages = self.short_term_memory[session_id]["messages"][-5:]  # Last 5 messages
            if recent_messages:
                context_parts.append("RECENT CONVERSATION:")
                for msg in recent_messages:
                    context_parts.append(f"User: {msg['user_message']}")
                    context_parts.append(f"Sofia: {msg['sofia_response']}")

        # Long-term memory
        user_key = f"user_{session_id}" if session_id else "anonymous"
        if user_key in self.long_term_memory:
            user_data = self.long_term_memory[user_key]
            context_parts.append(f"\nUSER PROFILE:")

            if "name" in user_data:
                context_parts.append(f"Name: {user_data['name']}")

            context_parts.append(f"Total conversations: {user_data['total_conversations']}")

            if user_data.get("preferences"):
                context_parts.append(f"Preferences: {user_data['preferences']}")

            if user_data.get("personal_info"):
                context_parts.append(f"Personal info: {user_data['personal_info']}")

            if user_data.get("relationship_notes"):
                context_parts.append(f"Relationship notes: {', '.join(user_data['relationship_notes'][-3:])}")

        return "\n".join(context_parts) if context_parts else ""

    def extract_user_info(self, message: str, response: str) -> Dict:
        """Extract user information from conversation"""
        info = {}
        message_lower = message.lower()

        # Extract name
        name_indicators = ["my name is", "i'm", "i am", "call me"]
        for indicator in name_indicators:
            if indicator in message_lower:
                parts = message_lower.split(indicator)
                if len(parts) > 1:
                    potential_name = parts[1].strip().split()[0]
                    if len(potential_name) > 1 and potential_name.isalpha():
                        info["name"] = potential_name.title()

        # Extract preferences
        preferences = {}
        if "prefer" in message_lower:
            preferences["mentioned_preference"] = message
        if "like" in message_lower or "love" in message_lower:
            preferences["likes"] = message
        if "don't like" in message_lower or "hate" in message_lower:
            preferences["dislikes"] = message

        if preferences:
            info["preferences"] = preferences

        # Extract personal info
        personal = {}
        if any(word in message_lower for word in ["work", "job", "career"]):
            personal["work_related"] = message
        if any(word in message_lower for word in ["family", "kids", "children", "spouse", "partner"]):
            personal["family_related"] = message
        if any(word in message_lower for word in ["hobby", "interest", "enjoy", "fun"]):
            personal["interests"] = message

        if personal:
            info["personal_info"] = personal

        return info

# Initialize memory system
conversation_memory = ConversationMemory()

# Legacy memory for backward compatibility
memory = ConversationBufferMemory(return_messages=True)

# Booking processing functions
def try_process_booking(user_message: str, memory_context: str) -> Dict:
    """Try to extract booking information and process it"""
    import re

    # Extract potential booking information
    name_match = re.search(r'(?:name is|i\'m|call me)\s+([a-zA-Z\s]+)', user_message.lower())
    date_match = re.search(r'(\d{4}-\d{2}-\d{2}|\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|today|tomorrow|monday|tuesday|wednesday|thursday|friday|saturday|sunday)', user_message.lower())
    time_match = re.search(r'(\d{1,2}:\d{2}|\d{1,2}\s*(?:am|pm))', user_message.lower())

    # Check if we have enough information to book
    has_name = name_match or "Name:" in memory_context
    has_service = any(word in user_message.lower() for word in ["consultation", "checkup", "meeting", "appointment", "session"])
    has_date = date_match is not None
    has_time = time_match is not None

    if has_name and has_service and has_date and has_time:
        # Extract information
        name = name_match.group(1).strip().title() if name_match else extract_name_from_memory(memory_context)
        service = "General Consultation"  # Default service
        date_str = date_match.group(1) if date_match else ""
        time_str = time_match.group(1) if time_match else ""

        # Normalize date format
        normalized_date = normalize_date(date_str)
        normalized_time = normalize_time(time_str)

        if normalized_date and normalized_time:
            # Try to book the appointment
            booking_result = book_appointment(name, service, normalized_date, normalized_time)

            if booking_result.get("success"):
                return {
                    "processed": True,
                    "response": booking_result["confirmation"]
                }
            else:
                return {
                    "processed": True,
                    "response": f"I'm sorry, but {booking_result.get('error', 'there was an issue with booking')}. Would you like to try a different time?"
                }

    return {"processed": False}

def try_process_inquiry(user_message: str, memory_context: str) -> Dict:
    """Try to process appointment inquiry"""
    import re

    # Look for appointment ID
    id_match = re.search(r'(?:id|booking|reference)\s*:?\s*([a-zA-Z0-9]{6,8})', user_message.lower())
    name_match = re.search(r'(?:name is|i\'m|for)\s+([a-zA-Z\s]+)', user_message.lower())

    if id_match:
        appointment_id = id_match.group(1)
        appointment = get_appointment_by_id(appointment_id)
        if appointment:
            return {
                "processed": True,
                "response": f"📅 Found your appointment!\n\nBooking ID: {appointment['appointment_id']}\nName: {appointment['name']}\nService: {appointment['appointment_type']}\nDate: {appointment['date']}\nTime: {appointment['time']}\nStatus: {appointment['status']}"
            }
        else:
            return {
                "processed": True,
                "response": f"I couldn't find an appointment with ID {appointment_id}. Please check the ID or provide your name."
            }

    elif name_match or "Name:" in memory_context:
        name = name_match.group(1).strip().title() if name_match else extract_name_from_memory(memory_context)
        if name:
            appointments = get_appointments_by_name(name)
            if appointments:
                response = f"📅 Found {len(appointments)} appointment(s) for {name}:\n\n"
                for apt in appointments:
                    response += f"• ID: {apt['appointment_id']} - {apt['appointment_type']} on {apt['date']} at {apt['time']}\n"
                return {"processed": True, "response": response}
            else:
                return {
                    "processed": True,
                    "response": f"I couldn't find any appointments for {name}. Would you like to book one?"
                }

    return {"processed": False}

def try_process_cancellation(user_message: str, memory_context: str) -> Dict:
    """Try to process appointment cancellation"""
    import re

    # Look for appointment ID
    id_match = re.search(r'(?:id|booking|reference)\s*:?\s*([a-zA-Z0-9]{6,8})', user_message.lower())

    if id_match:
        appointment_id = id_match.group(1)
        result = cancel_appointment(appointment_id)
        if result.get("success"):
            return {
                "processed": True,
                "response": result["message"]
            }
        else:
            return {
                "processed": True,
                "response": f"I couldn't cancel appointment {appointment_id}. {result.get('error', 'Please check the ID.')}"
            }

    return {"processed": False}

def extract_name_from_memory(memory_context: str) -> str:
    """Extract name from memory context"""
    if "Name:" in memory_context:
        for line in memory_context.split('\n'):
            if 'Name:' in line:
                return line.split('Name:')[1].strip()
    return ""

def normalize_date(date_str: str) -> str:
    """Normalize date to YYYY-MM-DD format"""
    from datetime import datetime, timedelta

    date_str = date_str.lower().strip()

    if date_str == "today":
        return datetime.now().strftime("%Y-%m-%d")
    elif date_str == "tomorrow":
        return (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")

    # Try to parse various date formats
    try:
        if "/" in date_str:
            parts = date_str.split("/")
            if len(parts) == 3:
                month, day, year = parts
                if len(year) == 2:
                    year = "20" + year
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        elif "-" in date_str:
            return date_str  # Assume already in correct format
    except:
        pass

    return ""

def normalize_time(time_str: str) -> str:
    """Normalize time to HH:MM format"""
    time_str = time_str.lower().strip()

    if "am" in time_str or "pm" in time_str:
        # Convert 12-hour to 24-hour format
        time_part = time_str.replace("am", "").replace("pm", "").strip()
        is_pm = "pm" in time_str

        if ":" in time_part:
            hour, minute = time_part.split(":")
        else:
            hour = time_part
            minute = "00"

        hour = int(hour)
        if is_pm and hour != 12:
            hour += 12
        elif not is_pm and hour == 12:
            hour = 0

        return f"{hour:02d}:{minute}"

    return time_str

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"Hello! I'm your AI appointment booking assistant. You said: '{user_message}'. How can I help you today?",
        "current_node": "greeting"
    }

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    try:
        answer = llm.invoke(question)
        return {
            "response": answer.content,
            "current_node": "faq"
        }
    except Exception as e:
        return {
            "response": f"I can help you with appointment booking. You asked: '{question}'. What would you like to know?",
            "current_node": "faq",
            "error": str(e)
        }

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    try:
        prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
        intent = llm.invoke(prompt)
        detected_intent = "book" if "yes" in intent.content.lower() else "other"
    except Exception:
        # Fallback intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve"]
        detected_intent = "book" if any(word in message.lower() for word in booking_keywords) else "other"

    return {
        "intent": detected_intent,
        "current_node": "intent_detection"
    }

def info_collection_node(state):
    # Simplified info collection
    user_message = state.get("user_message", "")
    return {
        "response": f"To book an appointment, I'll need your name, preferred date, time, and type of appointment. You said: '{user_message}'. What would you like to schedule?",
        "info_complete": False,
        "current_node": "info_collection"
    }

def fallback_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"I'm here to help with appointment booking. You said: '{user_message}'. Could you please tell me what you'd like to do?",
        "current_node": "fallback"
    }

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")

# Conditional edges for intent detection
def route_intent(state):
    intent = state.get("intent", "other")
    if intent == "book":
        return "info_collection"
    else:
        return "faq"

graph.add_conditional_edges("intent_detection", route_intent, {
    "info_collection": "info_collection",
    "faq": "faq"
})

# Simple edges to END
graph.add_edge("info_collection", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Compile the graph
compiled_graph = graph.compile()

# Complete Human-Like Attractive Agent with Memory
async def run_agent(user_message, conversation_id=None):
    try:
        # Get conversation context from memory
        memory_context = conversation_memory.get_conversation_context(conversation_id)

        # Analyze the message with emotional intelligence
        message_lower = user_message.lower().strip()

        # Enhanced greeting detection with warmth levels
        greetings = ["hello", "hi", "hey", "good morning", "good afternoon", "good evening", "howdy", "what's up"]
        enthusiastic_greetings = ["hey there", "hello there", "hi there", "good to see you"]
        is_greeting = any(greeting in message_lower for greeting in greetings)
        is_enthusiastic = any(greeting in message_lower for greeting in enthusiastic_greetings)

        # Sophisticated booking intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve", "appoint", "meeting", "consultation", "session", "visit"]
        urgent_booking = ["urgent", "asap", "soon", "today", "tomorrow", "emergency"]
        is_booking_intent = any(word in message_lower for word in booking_keywords)
        is_urgent = any(word in message_lower for word in urgent_booking)

        # Appointment inquiry detection
        inquiry_keywords = ["check", "view", "see", "find", "my appointment", "booking", "scheduled", "when is", "what time"]
        cancel_keywords = ["cancel", "delete", "remove", "cancel appointment"]
        reschedule_keywords = ["reschedule", "change", "move", "different time", "different date"]
        is_inquiry = any(word in message_lower for word in inquiry_keywords)
        is_cancel = any(word in message_lower for word in cancel_keywords)
        is_reschedule = any(word in message_lower for word in reschedule_keywords)

        # Enhanced question detection
        question_words = ["what", "when", "where", "how", "why", "which", "who", "can you", "do you", "are you", "could you", "would you"]
        is_question = any(word in message_lower for word in question_words) or message_lower.endswith("?")

        # Emotional state detection
        positive_words = ["great", "awesome", "wonderful", "excited", "happy", "love", "perfect", "amazing"]
        negative_words = ["stressed", "worried", "anxious", "frustrated", "upset", "problem", "issue", "difficult"]
        is_positive = any(word in message_lower for word in positive_words)
        is_negative = any(word in message_lower for word in negative_words)

        # Help/assistance detection with urgency
        help_keywords = ["help", "assist", "support", "need", "want", "looking for", "guidance", "advice"]
        needs_help = any(word in message_lower for word in help_keywords)

        # Compliment detection
        compliments = ["thank you", "thanks", "appreciate", "helpful", "great job", "amazing", "wonderful"]
        is_compliment = any(word in message_lower for word in compliments)

        # Create highly personalized conversational prompts with emotional intelligence and memory
        base_context = f"""
        MEMORY CONTEXT:
        {memory_context}

        CURRENT MESSAGE: "{user_message}"
        """

        if is_compliment:
            # Handle compliments gracefully but stay focused
            prompt = f"""You are Sofia, a professional appointment booking assistant with a warm but efficient personality.

            {base_context}

            The user complimented you. Acknowledge it warmly but briefly, then smoothly transition to helping them. If you know their name from memory, use it. Be gracious but keep it short and redirect to how you can assist them with appointments or questions.

            Example: "Thank you, that's so kind! I'm here to help you with appointments - what can I do for you today?"

            Be warm but focused on being helpful."""

        elif is_greeting and is_enthusiastic:
            # Enthusiastic greetings - friendly but focused
            prompt = f"""You are Sofia, a professional appointment booking assistant with a friendly personality.

            {base_context}

            The user greeted you enthusiastically! Respond warmly but efficiently. If you know them from memory, acknowledge it briefly. Be friendly and welcoming, then quickly ask how you can help them today with appointments or questions.

            Example: "Hi there! Great to hear from you again! How can I help you with your appointment needs today?"

            Be warm but get to the point quickly."""

        elif is_greeting and not is_booking_intent:
            # Regular greetings - friendly but direct
            prompt = f"""You are Sofia, a professional appointment booking assistant.

            {base_context}

            The user greeted you. Respond warmly but efficiently. If you know them from memory, acknowledge it. Be friendly but quickly transition to asking how you can help them with appointments or questions today.

            Example: "Hello! Nice to see you. How can I assist you with appointments today?"

            Be friendly but focused on helping."""

        elif is_cancel:
            # Cancel appointment
            prompt = f"""You are Sofia, a helpful appointment booking assistant. The user said: "{user_message}" and wants to cancel an appointment.

            {base_context}

            Help them cancel their appointment. Ask for their appointment ID or booking reference, or their name and appointment details to find it.

            Example: "I can help you cancel your appointment. Do you have your booking ID, or can you tell me your name and the appointment details?"

            Be helpful and understanding."""

        elif is_reschedule:
            # Reschedule appointment
            prompt = f"""You are Sofia, a helpful appointment booking assistant. The user said: "{user_message}" and wants to reschedule.

            {base_context}

            Help them reschedule their appointment. Ask for their current appointment details (ID or name/date) and when they'd like to move it to.

            Example: "I can help you reschedule. What's your current appointment ID or details, and when would you like to move it to?"

            Be accommodating and helpful."""

        elif is_inquiry:
            # Appointment inquiry
            prompt = f"""You are Sofia, a helpful appointment booking assistant. The user said: "{user_message}" and wants to check their appointment.

            {base_context}

            Help them find their appointment information. Ask for their appointment ID, name, or other details to look it up.

            Example: "I can help you check your appointment. What's your booking ID, or can you tell me your name and approximate date?"

            Be helpful in finding their information."""

        elif is_booking_intent and is_urgent:
            # Urgent booking - immediate action
            prompt = f"""You are Sofia, an efficient appointment booking assistant. The user said: "{user_message}" and needs urgent help.

            {base_context}

            Acknowledge their urgency immediately and get straight to business. Ask for the essential information: what type of appointment, when they need it, and their contact details. Be caring but focused on solving their problem quickly.

            Example: "I understand this is urgent. Let me help you right away. What type of appointment do you need and when?"

            Be empathetic but action-oriented."""

        elif is_booking_intent:
            # Regular booking - structured approach
            prompt = f"""You are Sofia, a professional appointment booking assistant. The user said: "{user_message}"

            {base_context}

            They want to book an appointment. Be friendly but efficient. Ask for the key information you need: type of appointment, preferred date/time, and their name. Use memory if you know them already.

            Example: "I'd be happy to help you book an appointment! What type of service are you looking for, and when would work best for you?"

            Be helpful but structured in gathering information."""

        elif is_negative:
            # Handle negative emotions - supportive but focused
            prompt = f"""You are Sofia, a caring appointment booking assistant. The user said: "{user_message}" and seems stressed.

            {base_context}

            Acknowledge their feelings briefly and offer to help. Be supportive but focus on how you can assist them practically with appointments or questions.

            Example: "I can hear you're having a tough time. I'm here to help make things easier - what can I do for you today?"

            Be empathetic but solution-focused."""

        elif is_positive:
            # Match positive energy but stay on task
            prompt = f"""You are Sofia, a friendly appointment booking assistant. The user said: "{user_message}" and seems happy!

            {base_context}

            Share their positive energy briefly, then ask how you can help them. Be upbeat but keep the focus on assisting them.

            Example: "I love your positive energy! How can I help you with appointments today?"

            Be cheerful but purposeful."""

        elif is_question:
            # Answer questions efficiently
            prompt = f"""You are Sofia, a knowledgeable appointment booking assistant. The user asked: "{user_message}"

            {base_context}

            Answer their question directly and helpfully. If it's about appointments, services, or scheduling, provide clear information. If you don't know something specific, be honest and suggest next steps.

            Example: "Great question! [Answer]. Is there anything else about appointments I can help you with?"

            Be informative and helpful."""

        elif needs_help:
            # Offer help efficiently
            prompt = f"""You are Sofia, a helpful appointment booking assistant. The user said: "{user_message}" and needs help.

            {base_context}

            Acknowledge their need for help and ask specifically what you can assist them with regarding appointments or services.

            Example: "I'm here to help! What specifically can I assist you with regarding appointments today?"

            Be supportive but direct."""

        else:
            # General conversation - redirect to purpose
            prompt = f"""You are Sofia, a professional appointment booking assistant. The user said: "{user_message}"

            {base_context}

            Respond briefly to what they said, then guide the conversation toward how you can help them with appointments or questions.

            Example: "I understand. How can I help you with appointments or answer any questions you have today?"

            Be friendly but purposeful."""

        # Check if user provided booking information and try to process it
        booking_processed = False
        if is_booking_intent:
            booking_result = try_process_booking(user_message, memory_context)
            if booking_result["processed"]:
                response = booking_result["response"]
                booking_processed = True

        # Check if user wants to inquire about appointments
        inquiry_processed = False
        if is_inquiry and not booking_processed:
            inquiry_result = try_process_inquiry(user_message, memory_context)
            if inquiry_result["processed"]:
                response = inquiry_result["response"]
                inquiry_processed = True

        # Check if user wants to cancel
        cancel_processed = False
        if is_cancel and not booking_processed and not inquiry_processed:
            cancel_result = try_process_cancellation(user_message, memory_context)
            if cancel_result["processed"]:
                response = cancel_result["response"]
                cancel_processed = True

        # If no specific action was processed, get AI response
        if not booking_processed and not inquiry_processed and not cancel_processed:
            try:
                llm_response = llm.invoke(prompt)
                response = llm_response.content
            except Exception as e:
                # Focused fallback responses
                if is_compliment:
                    response = "Thank you, that's very kind! How can I help you with appointments today?"
                elif is_greeting and is_enthusiastic:
                    response = "Hello! Great to meet you. I'm Sofia, your appointment assistant. How can I help you today?"
                elif is_greeting:
                    response = "Hi there! I'm Sofia. How can I assist you with appointments today?"
                elif is_booking_intent and is_urgent:
                    response = "I understand this is urgent. Let me help you right away. What type of appointment do you need and when?"
                elif is_booking_intent:
                    response = "I'd be happy to help you book an appointment! What type of service are you looking for, and when would work best?"
                elif is_negative:
                    response = f"I can hear you're having a tough time. I'm here to help make things easier - what can I do for you with appointments today?"
                elif is_positive:
                    response = f"I love your positive energy! How can I help you with appointments today?"
                elif needs_help:
                    response = "I'm here to help! What specifically can I assist you with regarding appointments?"
                else:
                    response = f"I'm Sofia, your appointment assistant. How can I help you today?"

        # Enhanced intent tracking with emotional context
        intent_details = {
            "primary_intent": "booking" if is_booking_intent else "question" if is_question else "greeting" if is_greeting else "help" if needs_help else "general",
            "emotional_state": "positive" if is_positive else "negative" if is_negative else "neutral",
            "urgency": "high" if is_urgent else "normal",
            "engagement_level": "enthusiastic" if is_enthusiastic else "complimentary" if is_compliment else "standard"
        }

        # Save to memory
        if conversation_id:
            # Add to short-term memory
            conversation_memory.add_to_short_term(conversation_id, user_message, response, intent_details)

            # Extract and save user info to long-term memory
            user_info = conversation_memory.extract_user_info(user_message, response)
            if user_info:
                conversation_memory.add_to_long_term(conversation_id, user_info)

        return response, {
            "user_message": user_message,
            "conversation_id": conversation_id,
            "intent_analysis": intent_details,
            "conversation_style": "attractive_human_like_with_memory",
            "agent_personality": "Sofia - Charming & Engaging with Memory",
            "method": "emotional_ai_with_memory",
            "memory_used": bool(memory_context)
        }

    except Exception as e:
        # Charming fallback response with memory attempt
        try:
            memory_context = conversation_memory.get_conversation_context(conversation_id)
            if memory_context and "Name:" in memory_context:
                # Extract name from memory context
                name_line = [line for line in memory_context.split('\n') if 'Name:' in line]
                if name_line:
                    name = name_line[0].split('Name:')[1].strip()
                    fallback_response = f"Hi {name}! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I remember our previous chats, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"
                else:
                    fallback_response = f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"
            else:
                fallback_response = f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"
        except:
            fallback_response = f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"

        return fallback_response, {
            "user_message": user_message,
            "conversation_id": conversation_id,
            "error": str(e),
            "method": "charming_fallback_with_memory",
            "agent_personality": "Sofia - Resilient & Charming with Memory"
        }