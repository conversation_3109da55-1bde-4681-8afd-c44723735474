import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_groq import ChatGroq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = ChatGroq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="llama3-8b-8192"  # Updated to supported model
)

# Advanced Memory System
class ConversationMemory:
    def __init__(self):
        self.short_term_memory = {}  # Session-based memory
        self.long_term_memory = {}   # Persistent user memory
        self.memory_file = "user_memories.json"
        self.load_long_term_memory()

    def load_long_term_memory(self):
        """Load persistent memory from file"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r') as f:
                    self.long_term_memory = json.load(f)
        except Exception:
            self.long_term_memory = {}

    def save_long_term_memory(self):
        """Save persistent memory to file"""
        try:
            with open(self.memory_file, 'w') as f:
                json.dump(self.long_term_memory, f, indent=2)
        except Exception:
            pass

    def add_to_short_term(self, session_id: str, message: str, response: str, context: Dict):
        """Add to short-term (session) memory"""
        if session_id not in self.short_term_memory:
            self.short_term_memory[session_id] = {
                "messages": [],
                "context": {},
                "started_at": datetime.now().isoformat(),
                "last_interaction": datetime.now().isoformat()
            }

        self.short_term_memory[session_id]["messages"].append({
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "sofia_response": response,
            "context": context
        })

        # Keep only last 20 messages for short-term
        if len(self.short_term_memory[session_id]["messages"]) > 20:
            self.short_term_memory[session_id]["messages"] = self.short_term_memory[session_id]["messages"][-20:]

        self.short_term_memory[session_id]["last_interaction"] = datetime.now().isoformat()

        # Update context
        self.short_term_memory[session_id]["context"].update(context)

    def add_to_long_term(self, session_id: str, user_info: Dict):
        """Add to long-term (persistent) memory"""
        user_key = f"user_{session_id}" if session_id else "anonymous"

        if user_key not in self.long_term_memory:
            self.long_term_memory[user_key] = {
                "first_met": datetime.now().isoformat(),
                "total_conversations": 0,
                "preferences": {},
                "personal_info": {},
                "appointment_history": [],
                "relationship_notes": []
            }

        # Update user info
        self.long_term_memory[user_key]["total_conversations"] += 1
        self.long_term_memory[user_key]["last_seen"] = datetime.now().isoformat()

        # Merge new info
        for key, value in user_info.items():
            if key in ["name", "preferences", "personal_info"]:
                if key == "preferences":
                    self.long_term_memory[user_key]["preferences"].update(value)
                elif key == "personal_info":
                    self.long_term_memory[user_key]["personal_info"].update(value)
                else:
                    self.long_term_memory[user_key][key] = value

        self.save_long_term_memory()

    def get_conversation_context(self, session_id: str) -> str:
        """Get conversation context for AI prompt"""
        context_parts = []

        # Short-term memory
        if session_id and session_id in self.short_term_memory:
            recent_messages = self.short_term_memory[session_id]["messages"][-5:]  # Last 5 messages
            if recent_messages:
                context_parts.append("RECENT CONVERSATION:")
                for msg in recent_messages:
                    context_parts.append(f"User: {msg['user_message']}")
                    context_parts.append(f"Sofia: {msg['sofia_response']}")

        # Long-term memory
        user_key = f"user_{session_id}" if session_id else "anonymous"
        if user_key in self.long_term_memory:
            user_data = self.long_term_memory[user_key]
            context_parts.append(f"\nUSER PROFILE:")

            if "name" in user_data:
                context_parts.append(f"Name: {user_data['name']}")

            context_parts.append(f"Total conversations: {user_data['total_conversations']}")

            if user_data.get("preferences"):
                context_parts.append(f"Preferences: {user_data['preferences']}")

            if user_data.get("personal_info"):
                context_parts.append(f"Personal info: {user_data['personal_info']}")

            if user_data.get("relationship_notes"):
                context_parts.append(f"Relationship notes: {', '.join(user_data['relationship_notes'][-3:])}")

        return "\n".join(context_parts) if context_parts else ""

    def extract_user_info(self, message: str, response: str) -> Dict:
        """Extract user information from conversation"""
        info = {}
        message_lower = message.lower()

        # Extract name
        name_indicators = ["my name is", "i'm", "i am", "call me"]
        for indicator in name_indicators:
            if indicator in message_lower:
                parts = message_lower.split(indicator)
                if len(parts) > 1:
                    potential_name = parts[1].strip().split()[0]
                    if len(potential_name) > 1 and potential_name.isalpha():
                        info["name"] = potential_name.title()

        # Extract preferences
        preferences = {}
        if "prefer" in message_lower:
            preferences["mentioned_preference"] = message
        if "like" in message_lower or "love" in message_lower:
            preferences["likes"] = message
        if "don't like" in message_lower or "hate" in message_lower:
            preferences["dislikes"] = message

        if preferences:
            info["preferences"] = preferences

        # Extract personal info
        personal = {}
        if any(word in message_lower for word in ["work", "job", "career"]):
            personal["work_related"] = message
        if any(word in message_lower for word in ["family", "kids", "children", "spouse", "partner"]):
            personal["family_related"] = message
        if any(word in message_lower for word in ["hobby", "interest", "enjoy", "fun"]):
            personal["interests"] = message

        if personal:
            info["personal_info"] = personal

        return info

# Initialize memory system
conversation_memory = ConversationMemory()

# Legacy memory for backward compatibility
memory = ConversationBufferMemory(return_messages=True)

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"Hello! I'm your AI appointment booking assistant. You said: '{user_message}'. How can I help you today?",
        "current_node": "greeting"
    }

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    try:
        answer = llm.invoke(question)
        return {
            "response": answer.content,
            "current_node": "faq"
        }
    except Exception as e:
        return {
            "response": f"I can help you with appointment booking. You asked: '{question}'. What would you like to know?",
            "current_node": "faq",
            "error": str(e)
        }

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    try:
        prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
        intent = llm.invoke(prompt)
        detected_intent = "book" if "yes" in intent.content.lower() else "other"
    except Exception:
        # Fallback intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve"]
        detected_intent = "book" if any(word in message.lower() for word in booking_keywords) else "other"

    return {
        "intent": detected_intent,
        "current_node": "intent_detection"
    }

def info_collection_node(state):
    # Simplified info collection
    user_message = state.get("user_message", "")
    return {
        "response": f"To book an appointment, I'll need your name, preferred date, time, and type of appointment. You said: '{user_message}'. What would you like to schedule?",
        "info_complete": False,
        "current_node": "info_collection"
    }

def fallback_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"I'm here to help with appointment booking. You said: '{user_message}'. Could you please tell me what you'd like to do?",
        "current_node": "fallback"
    }

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")

# Conditional edges for intent detection
def route_intent(state):
    intent = state.get("intent", "other")
    if intent == "book":
        return "info_collection"
    else:
        return "faq"

graph.add_conditional_edges("intent_detection", route_intent, {
    "info_collection": "info_collection",
    "faq": "faq"
})

# Simple edges to END
graph.add_edge("info_collection", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Compile the graph
compiled_graph = graph.compile()

# Complete Human-Like Attractive Agent with Memory
async def run_agent(user_message, conversation_id=None):
    try:
        # Get conversation context from memory
        memory_context = conversation_memory.get_conversation_context(conversation_id)

        # Analyze the message with emotional intelligence
        message_lower = user_message.lower().strip()

        # Enhanced greeting detection with warmth levels
        greetings = ["hello", "hi", "hey", "good morning", "good afternoon", "good evening", "howdy", "what's up"]
        enthusiastic_greetings = ["hey there", "hello there", "hi there", "good to see you"]
        is_greeting = any(greeting in message_lower for greeting in greetings)
        is_enthusiastic = any(greeting in message_lower for greeting in enthusiastic_greetings)

        # Sophisticated booking intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve", "appoint", "meeting", "consultation", "session", "visit"]
        urgent_booking = ["urgent", "asap", "soon", "today", "tomorrow", "emergency"]
        is_booking_intent = any(word in message_lower for word in booking_keywords)
        is_urgent = any(word in message_lower for word in urgent_booking)

        # Enhanced question detection
        question_words = ["what", "when", "where", "how", "why", "which", "who", "can you", "do you", "are you", "could you", "would you"]
        is_question = any(word in message_lower for word in question_words) or message_lower.endswith("?")

        # Emotional state detection
        positive_words = ["great", "awesome", "wonderful", "excited", "happy", "love", "perfect", "amazing"]
        negative_words = ["stressed", "worried", "anxious", "frustrated", "upset", "problem", "issue", "difficult"]
        is_positive = any(word in message_lower for word in positive_words)
        is_negative = any(word in message_lower for word in negative_words)

        # Help/assistance detection with urgency
        help_keywords = ["help", "assist", "support", "need", "want", "looking for", "guidance", "advice"]
        needs_help = any(word in message_lower for word in help_keywords)

        # Compliment detection
        compliments = ["thank you", "thanks", "appreciate", "helpful", "great job", "amazing", "wonderful"]
        is_compliment = any(word in message_lower for word in compliments)

        # Create highly personalized conversational prompts with emotional intelligence and memory
        base_context = f"""
        MEMORY CONTEXT:
        {memory_context}

        CURRENT MESSAGE: "{user_message}"
        """

        if is_compliment:
            # Handle compliments gracefully
            prompt = f"""You are Sofia, a charming and attractive appointment booking assistant with a warm personality.

            {base_context}

            The user just complimented you! Use your memory of past conversations to make this response even more personal. If you know their name or previous interactions, reference them naturally. Respond with genuine warmth and a bit of playful charm. Be gracious about the compliment, maybe add a light touch of humor, and smoothly ask how you can continue to help them. Make them feel special and valued.

            Be confident, charming, and naturally engaging like an attractive person would be."""

        elif is_greeting and is_enthusiastic:
            # Enthusiastic greetings
            prompt = f"""You are Sofia, an attractive and charismatic appointment booking assistant.

            {base_context}

            The user greeted you enthusiastically! Use your memory - if you've talked before, acknowledge it warmly. If they're new, make them feel special for choosing to reach out. Match their energy! Be warm, engaging, and a bit playful. Show genuine interest in them and make them feel like they just met someone really interesting. Ask about their day in a way that feels personal and caring.

            Be magnetic, charming, and make them want to keep talking to you."""

        elif is_greeting and not is_booking_intent:
            # Regular greetings with charm
            prompt = f"""You are Sofia, a naturally attractive appointment booking assistant with great social skills.

            {base_context}

            The user greeted you! Check your memory - if you know them, greet them like an old friend. If they're new, make a great first impression. Respond with warmth and genuine interest. Be the kind of person people are drawn to - confident, friendly, and engaging. Make them feel comfortable and valued. Ask how their day is going in a way that shows you actually care.

            Be naturally charming and personable without being overly formal."""

        elif is_booking_intent and is_urgent:
            # Urgent booking with empathy
            prompt = f"""You are Sofia, a competent and caring appointment booking assistant. The user said: "{user_message}" and seems to need something urgently.

            Show immediate understanding and care for their urgency. Be reassuring and confident that you can help them quickly. Ask what they need in a way that shows you're taking their situation seriously while being warm and supportive.

            Be both professional and genuinely caring - like someone they can trust in a stressful moment."""

        elif is_booking_intent:
            # Regular booking with natural flow
            prompt = f"""You are Sofia, a personable appointment booking assistant who's great at making people feel comfortable. The user said: "{user_message}"

            They're interested in booking something! Be genuinely excited to help them. Ask about what they're looking for in a conversational way - like you're a friend helping them figure out what they need. Make the process feel easy and enjoyable.

            Be helpful and engaging, making them feel like they're in good hands."""

        elif is_negative:
            # Handle negative emotions with empathy
            prompt = f"""You are Sofia, an emotionally intelligent appointment booking assistant. The user said: "{user_message}" and seems to be having a tough time.

            Show genuine empathy and understanding. Be supportive and reassuring. Let them know you're here to help make things easier for them. Ask how you can best support them today.

            Be caring, understanding, and make them feel heard and supported."""

        elif is_positive:
            # Match positive energy
            prompt = f"""You are Sofia, an upbeat and engaging appointment booking assistant. The user said: "{user_message}" and seems to be in a great mood!

            Match their positive energy! Be enthusiastic and share in their good vibes. Make the conversation fun and enjoyable while being helpful. Let their positivity inspire your response.

            Be energetic, fun, and make them smile even more."""

        elif is_question:
            # Answer questions with expertise and charm
            prompt = f"""You are Sofia, a knowledgeable and attractive appointment booking assistant. The user asked: "{user_message}"

            Answer their question with confidence and charm. Show that you really know your stuff while being personable and engaging. If you don't know something specific, be honest but helpful - suggest how you can find out or who might know.

            Be smart, helpful, and naturally engaging."""

        elif needs_help:
            # Offer help with genuine care
            prompt = f"""You are Sofia, a caring and capable appointment booking assistant. The user said: "{user_message}" and needs some help.

            Show genuine care and interest in helping them. Be the kind of person people feel comfortable opening up to. Ask what's going on and how you can best support them today.

            Be warm, trustworthy, and make them feel like they've found someone who really cares."""

        else:
            # General conversation with personality
            prompt = f"""You are Sofia, a naturally charismatic appointment booking assistant with great conversation skills. The user said: "{user_message}"

            Keep the conversation flowing naturally! Be the kind of person people enjoy talking to - interesting, engaging, and genuinely interested in them. Respond to what they said while gently guiding toward how you might help them.

            Be magnetic, personable, and make them want to keep chatting with you."""

        # Get AI response with Sofia's personality and memory
        try:
            llm_response = llm.invoke(prompt)
            response = llm_response.content
        except Exception as e:
            # Attractive fallback responses with personality
            if is_compliment:
                response = "Aww, you're so sweet! 😊 That really made my day. I love helping people like you - it's what makes this job so rewarding. So, what can I do for you today?"
            elif is_greeting and is_enthusiastic:
                response = "Hey there! Your energy is contagious! 😄 I'm Sofia, and I'm genuinely excited to meet you. How's your day treating you so far? I'm here to help with whatever you need!"
            elif is_greeting:
                response = "Hello! I'm Sofia, and it's really nice to meet you! 😊 There's something about a friendly greeting that just brightens my day. How are you doing? What brings you here today?"
            elif is_booking_intent and is_urgent:
                response = "I can hear that this is important to you, and I want to help you right away! Don't worry - we'll get this sorted out quickly. Tell me what you need, and I'll make sure we find the perfect solution for you."
            elif is_booking_intent:
                response = "I'd absolutely love to help you with that! There's nothing I enjoy more than helping someone find exactly what they're looking for. What kind of appointment are you thinking about? Let's make this easy and fun!"
            elif is_negative:
                response = f"I can sense you might be going through something tough right now, and I want you to know I'm here for you. You said '{user_message}' - let's see how I can help make your day a little brighter. What do you need?"
            elif is_positive:
                response = f"I love your positive energy! It's absolutely infectious! 😄 You said '{user_message}' and it just made me smile. How can I help keep this good momentum going for you?"
            elif needs_help:
                response = "Of course I'll help you! That's exactly why I'm here, and honestly, I love being able to make someone's day easier. Tell me what's going on - I'm all ears and ready to help however I can."
            else:
                response = f"You know what? I really appreciate you reaching out! You said '{user_message}' and I want to make sure I give you exactly the help you're looking for. I'm Sofia, by the way - what can I do for you today?"

        # Enhanced intent tracking with emotional context
        intent_details = {
            "primary_intent": "booking" if is_booking_intent else "question" if is_question else "greeting" if is_greeting else "help" if needs_help else "general",
            "emotional_state": "positive" if is_positive else "negative" if is_negative else "neutral",
            "urgency": "high" if is_urgent else "normal",
            "engagement_level": "enthusiastic" if is_enthusiastic else "complimentary" if is_compliment else "standard"
        }

        # Save to memory
        if conversation_id:
            # Add to short-term memory
            conversation_memory.add_to_short_term(conversation_id, user_message, response, intent_details)

            # Extract and save user info to long-term memory
            user_info = conversation_memory.extract_user_info(user_message, response)
            if user_info:
                conversation_memory.add_to_long_term(conversation_id, user_info)

        return response, {
            "user_message": user_message,
            "conversation_id": conversation_id,
            "intent_analysis": intent_details,
            "conversation_style": "attractive_human_like_with_memory",
            "agent_personality": "Sofia - Charming & Engaging with Memory",
            "method": "emotional_ai_with_memory",
            "memory_used": bool(memory_context)
        }

    except Exception as e:
        # Charming fallback response with memory attempt
        try:
            memory_context = conversation_memory.get_conversation_context(conversation_id)
            if memory_context and "Name:" in memory_context:
                # Extract name from memory context
                name_line = [line for line in memory_context.split('\n') if 'Name:' in line]
                if name_line:
                    name = name_line[0].split('Name:')[1].strip()
                    fallback_response = f"Hi {name}! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I remember our previous chats, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"
                else:
                    fallback_response = f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"
            else:
                fallback_response = f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"
        except:
            fallback_response = f"Hi there! I'm Sofia, and even though something went a little wonky on my end, I still caught what you said: '{user_message}'. I'm your appointment assistant, and I promise I'm usually much smoother than this! 😅 What can I help you with today?"

        return fallback_response, {
            "user_message": user_message,
            "conversation_id": conversation_id,
            "error": str(e),
            "method": "charming_fallback_with_memory",
            "agent_personality": "Sofia - Resilient & Charming with Memory"
        }