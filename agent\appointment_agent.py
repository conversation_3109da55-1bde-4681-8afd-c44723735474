import os
from langgraph.graph import StateGraph, END
from langchain.memory import ConversationBufferMemory
from langchain.tools import Tool
from langchain_groq import ChatGroq
from tools.calendar import check_availability, book_appointment

# Set up Groq LLM
llm = ChatGroq(
    api_key=os.getenv("GROQ_API_KEY"),
    model="llama3-8b-8192"  # Updated to supported model
)

# Memory for conversation state
memory = ConversationBufferMemory(return_messages=True)

# Define tools
calendar_tools = [
    Tool(
        name="check_availability",
        func=check_availability,
        description="Check if a time slot is available in the calendar."
    ),
    Tool(
        name="book_appointment",
        func=book_appointment,
        description="Book an appointment in the calendar."
    )
]

# Define LangGraph nodes (functions)
def greeting_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"Hello! I'm your AI appointment booking assistant. You said: '{user_message}'. How can I help you today?",
        "current_node": "greeting"
    }

def faq_node(state):
    # Use LLM to answer FAQs
    question = state.get("user_message", "")
    try:
        answer = llm.invoke(question)
        return {
            "response": answer.content,
            "current_node": "faq"
        }
    except Exception as e:
        return {
            "response": f"I can help you with appointment booking. You asked: '{question}'. What would you like to know?",
            "current_node": "faq",
            "error": str(e)
        }

def intent_detection_node(state):
    # Use LLM to detect if user wants to book
    message = state.get("user_message", "")
    try:
        prompt = f"Is the following message a booking intent? '{message}' Answer yes or no."
        intent = llm.invoke(prompt)
        detected_intent = "book" if "yes" in intent.content.lower() else "other"
    except Exception:
        # Fallback intent detection
        booking_keywords = ["book", "appointment", "schedule", "reserve"]
        detected_intent = "book" if any(word in message.lower() for word in booking_keywords) else "other"

    return {
        "intent": detected_intent,
        "current_node": "intent_detection"
    }

def info_collection_node(state):
    # Simplified info collection
    user_message = state.get("user_message", "")
    return {
        "response": f"To book an appointment, I'll need your name, preferred date, time, and type of appointment. You said: '{user_message}'. What would you like to schedule?",
        "info_complete": False,
        "current_node": "info_collection"
    }

def fallback_node(state):
    user_message = state.get("user_message", "")
    return {
        "response": f"I'm here to help with appointment booking. You said: '{user_message}'. Could you please tell me what you'd like to do?",
        "current_node": "fallback"
    }

# Build the LangGraph
class AppointmentState(dict):
    pass

graph = StateGraph(AppointmentState)
graph.add_node("greeting", greeting_node)
graph.add_node("faq", faq_node)
graph.add_node("intent_detection", intent_detection_node)
graph.add_node("info_collection", info_collection_node)
graph.add_node("fallback", fallback_node)

# Define edges (simplified for clarity)
graph.add_edge("greeting", "intent_detection")

# Conditional edges for intent detection
def route_intent(state):
    intent = state.get("intent", "other")
    if intent == "book":
        return "info_collection"
    else:
        return "faq"

graph.add_conditional_edges("intent_detection", route_intent, {
    "info_collection": "info_collection",
    "faq": "faq"
})

# Simple edges to END
graph.add_edge("info_collection", END)
graph.add_edge("faq", END)
graph.add_edge("fallback", END)

graph.set_entry_point("greeting")

# Compile the graph
compiled_graph = graph.compile()

# Exported function for FastAPI
async def run_agent(user_message, session_id=None):
    try:
        # Use session_id for memory if needed
        state = AppointmentState(user_message=user_message)
        result = compiled_graph.invoke(state)

        # Handle case where result might be None
        if result is None:
            return "I'm sorry, I encountered an issue processing your request. How can I help you with appointment booking?", {
                "user_message": user_message,
                "session_id": session_id,
                "error": "Graph returned None"
            }

        # Get response from result
        response = result.get("response", "I'm here to help with appointment booking. What would you like to do?")
        return response, dict(result)

    except Exception as e:
        # Fallback response
        return f"I'm your appointment booking assistant. You said: '{user_message}'. How can I help you today?", {
            "user_message": user_message,
            "session_id": session_id,
            "error": str(e)
        }