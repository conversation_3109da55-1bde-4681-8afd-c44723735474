import datetime

# In-memory mock calendar (for demo/testing)
mock_calendar = {}

def check_availability(date: str, time: str) -> bool:
    """
    Check if the given date and time slot is available.
    Returns True if available, False if already booked.
    """
    key = f"{date}T{time}"
    return key not in mock_calendar

def book_appointment(name: str, appointment_type: str, date: str, time: str) -> dict:
    """
    Book an appointment in the mock calendar.
    Returns appointment details.
    """
    key = f"{date}T{time}"
    if key in mock_calendar:
        return {"error": "Slot already booked."}
    mock_calendar[key] = {
        "name": name,
        "appointment_type": appointment_type,
        "date": date,
        "time": time,
        "created_at": datetime.datetime.now().isoformat()
    }
    return mock_calendar[key]

# For real Google Calendar integration, replace the above with API calls using google-api-python-client. 